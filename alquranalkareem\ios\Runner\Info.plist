<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>com.transistorsoft.fetch</string>
			<string>com.transistorsoft.fetchNotifications</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>القرآن الكريم - مكتبة الحكمة</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>القرآن الكريم - مكتبة الحكمة</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>NSBonjourServices</key>
		<array>
			<string>_dartobservatory._tcp</string>
		</array>
		<key>NSMicrophoneUsageDescription</key>
		<string>Our app uses the microphone to allow voice chatting during gameplay.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>App requires access to the photo library to save images</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>App requires access to the photo library to save images</string>
		<key>NSUserActivityTypes</key>
		<array>
			<string>ConfigurationIntent</string>
		</array>
		<key>UIApplicationSceneManifest</key>
		<dict>
			<key>UISceneConfigurations</key>
			<dict/>
		</dict>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
	</dict>
</plist>
