{"appName": "Al <PERSON> - Al Heekmah Library", "@appName": {"description": "appName", "type": "text", "placeholders": {}}, "search_hint": "Search the verses of the Quran", "@search_hint": {"description": "search_hint", "type": "text", "placeholders": {}}, "menu": "<PERSON><PERSON>", "@menu": {"description": "menu", "type": "text", "placeholders": {}}, "notes": "Notes", "@notes": {"description": "notes", "type": "text", "placeholders": {}}, "note_title": "Title", "@note_title": {"description": "note_title", "type": "text", "placeholders": {}}, "add_new_note": "Add a new note", "@add_new_note": {"type": "text", "placeholders": {}}, "note_details": "Note Details", "@note_details": {"type": "text", "placeholders": {}}, "bookmarks": "Bookmarks Saved", "@bookmarks": {"type": "text", "placeholders": {}}, "bookmark_title": "Bookmark Name", "@bookmark_title": {"type": "text", "placeholders": {}}, "add_new_bookmark": "Add a new Bookmark", "@add_new_bookmark": {"type": "text", "placeholders": {}}, "save": "Save", "@save": {"type": "text", "placeholders": {}}, "edit": "Edit", "@edit": {"type": "text", "placeholders": {}}, "azkar": "<PERSON><PERSON><PERSON>", "@azkar": {"type": "text", "placeholders": {}}, "qibla": "Qibla Direction", "@qibla": {"type": "text", "placeholders": {}}, "salat": "Prayer Times", "@salat": {"type": "text", "placeholders": {}}, "aya_count": "Number of verses", "@aya_count": {"description": "aya_count", "type": "text", "placeholders": {}}, "quran_sorah": "Surahs", "@quran_sorah": {"type": "text", "placeholders": {}}, "about_us": "About App", "@about_us": {"type": "text", "placeholders": {}}, "stop_title": "The noble Qur’an - Al-Heekmah Library", "@stop_title": {"type": "text", "placeholders": {}}, "about_app": "The \"Holy Quran - Hikma Library\" app is an integrated application that offers a wide range of features to enhance the experience of reading and learning the Holy Quran, including:", "@about_app": {"type": "text", "placeholders": {}}, "about_app2": "Among the most important features of the application :", "@about_app2": {"type": "text", "placeholders": {}}, "about_app3": "۞ A new and user-friendly interface: The application has been designed with a modern and intuitive user interface that makes it easy for users to navigate and access different features.\n۞ Adoption of the King Fahd Complex edition: The application uses the edition of the King Fahd Complex for the printing of the Holy Quran, known for its reliability and perfection, ensuring the accuracy and integrity of the text.\n۞ Interactive and integrated reading: The application provides the possibility of interactive reading with the electronic Quran text, in addition to listening to recitations, and studying and memorizing the Holy Quran easily.\n۞ Flexible reading: Users can read the Quran as if they were reading a paper copy or choose the single verse mode for greater focus.\n۞ Textual search feature: The application contains an instant search feature in the verses of the Quran, with the ability to go directly to the desired page or surah.\n۞ Adding bookmark badges: This feature allows saving pages or verses for easy reference at any time.\n۞ Listening to verses: The application provides the possibility to listen to each verse by a number of famous reciters.\n۞ Interpretation and translation of verses: Users can access the interpretation or translation of each verse, and switch between interpretations as desired.\n۞ Easy navigation between surahs: The application facilitates smooth and quick navigation between surahs.\n۞ Reading stop signs: This feature helps to understand the appropriate places to stop while reading.\n۞ Fortress of the Muslim Dhikr: It is possible to read the complete Fortress of the Muslim Dhikr and navigate between sections easily, with the possibility of adding Dhikr to favorites.\n۞ Changing color styles: The application supports changing color styles, including the dark mode, to improve the reading experience.\n۞ Listening and downloading: It is possible to listen to the surahs or download them for later listening without the need for the internet.\n\nThese features make the \"Holy Quran - Hikma Library\" app a comprehensive tool for anyone wishing to read, learn, and contemplate the Holy Quran in an easy and effective way.", "@about_app3": {"type": "text", "placeholders": {}}, "email": "Contact us", "@email": {"type": "text", "placeholders": {}}, "select_player": "Reader selected", "@select_player": {"type": "text", "placeholders": {}}, "delete": "Delete", "@delete": {"type": "text", "placeholders": {}}, "page": "Page", "@page": {"type": "text", "placeholders": {}}, "search_word": "Search for a Verse", "@search_word": {"type": "text", "placeholders": {}}, "search_description": "You can search for all verses of the Noble Qur’an, just type a word from the verse.", "@search_description": {"type": "text", "placeholders": {}}, "fontSize": "Change Font Size", "@fontSize": {"type": "text", "placeholders": {}}, "waqfName": "Stop Signs", "@waqfName": {"type": "text", "placeholders": {}}, "onboardTitle1": "Easy interface", "@onboardTitle1": {"type": "text", "placeholders": {}}, "onboardDesc1": "- Ease of searching for a verse.\n- Change the language.\n- Listen to the page.\n- Change the reader.", "@onboardDesc1": {"type": "text", "placeholders": {}}, "onboardTitle2": "Show The Tafseer", "@onboardTitle2": {"type": "text", "placeholders": {}}, "onboardDesc2": "The interpretation of each verse can be read by pulling the list up.", "@onboardDesc2": {"type": "text", "placeholders": {}}, "onboardTitle3": "Click Options", "@onboardTitle3": {"type": "text", "placeholders": {}}, "onboardDesc3": "1- When you double click the page is enlarged.\n2- Upon long click you will be presented with the option to save the page.\n3- When you press once, the menus appear.", "@onboardDesc3": {"type": "text", "placeholders": {}}, "green": "Green Mode", "@green": {"type": "text", "placeholders": {}}, "brown": "<PERSON>", "@brown": {"type": "text", "placeholders": {}}, "dark": "Dark Mode", "@dark": {"type": "text", "placeholders": {}}, "azkarfav": "<PERSON><PERSON><PERSON>", "@azkarfav": {"type": "text", "placeholders": {}}, "themeTitle": "<PERSON>ose <PERSON>", "@themeTitle": {"type": "text", "placeholders": {}}, "sorah": "<PERSON><PERSON>", "@sorah": {"type": "text", "placeholders": {}}, "part": "Part", "@part": {"type": "text", "placeholders": {}}, "langChange": "Change the language", "@langChange": {"type": "text", "placeholders": {}}, "tafChange": "Choose an tafsir", "@tafChange": {"type": "text", "placeholders": {}}, "tafIbnkatheerN": "<PERSON><PERSON><PERSON>", "@tafIbnkatheerN": {"type": "text", "placeholders": {}}, "tafBaghawyN": "<PERSON><PERSON><PERSON>", "@tafBaghawyN": {"type": "text", "placeholders": {}}, "tafQurtubiN": "<PERSON><PERSON><PERSON>", "@tafQurtubiN": {"type": "text", "placeholders": {}}, "tafSaadiN": "<PERSON><PERSON><PERSON>", "@tafSaadiN": {"type": "text", "placeholders": {}}, "tafTabariN": "Tafsir <PERSON>", "@tafTabariN": {"type": "text", "placeholders": {}}, "tafIbnkatheerD": "<PERSON><PERSON><PERSON> <PERSON><PERSON> al-Azi<PERSON>", "@tafIbnkatheerD": {"type": "text", "placeholders": {}}, "tafBaghawyD": "<PERSON><PERSON><PERSON>", "@tafBaghawyD": {"type": "text", "placeholders": {}}, "tafQurtubiD": "<PERSON><PERSON><PERSON><PERSON> <PERSON>i <PERSON> al<PERSON>", "@tafQurtubiD": {"type": "text", "placeholders": {}}, "tafSaadiD": "<PERSON><PERSON><PERSON>", "@tafSaadiD": {"type": "text", "placeholders": {}}, "tafTabariD": "<PERSON><PERSON> an <PERSON> [ay] al <PERSON>", "@tafTabariD": {"type": "text", "placeholders": {}}, "appLang": "App Language", "@appLang": {"type": "text", "placeholders": {}}, "setting": "Setting", "@setting": {"type": "text", "placeholders": {}}, "reader1": "<PERSON>", "@reader1": {"type": "text", "placeholders": {}}, "reader2": "<PERSON>", "@reader2": {"type": "text", "placeholders": {}}, "reader3": "<PERSON><PERSON><PERSON>", "@reader3": {"type": "text", "placeholders": {}}, "reader4": "<PERSON>", "@reader4": {"type": "text", "placeholders": {}}, "reader5": "<PERSON><PERSON>", "@reader5": {"type": "text", "placeholders": {}}, "reader6": "<PERSON><PERSON>", "@reader6": {"type": "text", "placeholders": {}}, "reader7": "<PERSON><PERSON>", "@reader7": {"type": "text", "placeholders": {}}, "reader8": "<PERSON>", "@reader8": {"type": "text", "placeholders": {}}, "reader9": "<PERSON><PERSON>", "@reader9": {"type": "text", "placeholders": {}}, "reader10": "<PERSON>ader <PERSON>", "@reader10": {"type": "text", "placeholders": {}}, "reader11": "<PERSON><PERSON><PERSON>", "@reader11": {"type": "text", "placeholders": {}}, "reader12": "<PERSON><PERSON><PERSON>", "@reader12": {"type": "text", "placeholders": {}}, "reader13": "Wadi <PERSON>-<PERSON>", "@reader13": {"type": "text", "placeholders": {}}, "reader14": "<PERSON><PERSON>", "@reader14": {"type": "text", "placeholders": {}}, "reader15": "<PERSON>", "@reader15": {"type": "text", "placeholders": {}}, "reader16": "<PERSON><PERSON>", "@reader16": {"type": "text", "placeholders": {}}, "reader17": "<PERSON>", "@reader17": {"type": "text", "placeholders": {}}, "reader18": "<PERSON><PERSON>", "@reader18": {"type": "text", "placeholders": {}}, "reader19": "<PERSON>", "@reader19": {"type": "text", "placeholders": {}}, "reader20": "<PERSON><PERSON>", "@reader20": {"type": "text", "placeholders": {}}, "alheekmahlib": "Alheekmah Library", "@alheekmahlib": {"type": "text", "placeholders": {}}, "backTo": "Back To", "@backTo": {"type": "text", "placeholders": {}}, "next": "Next", "@next": {"type": "text", "placeholders": {}}, "start": "Start", "@start": {"type": "text", "placeholders": {}}, "quranPages": "<PERSON> (Pages)", "@quranPages": {"type": "text", "placeholders": {}}, "quranText": "<PERSON> (Ayah)", "@quranText": {"type": "text", "placeholders": {}}, "tafseer": "Al <PERSON>", "@tafseer": {"type": "text", "placeholders": {}}, "allJuz": "Parts", "@allJuz": {"type": "text", "placeholders": {}}, "copyAzkarText": "The Azkar has been copied!", "@copyAzkarText": {"type": "text", "placeholders": {}}, "addBookmark": "Bookmark added!", "@addBookmark": {"type": "text", "placeholders": {}}, "deletedBookmark": "Bookmark deleted!", "@deletedBookmark": {"type": "text", "placeholders": {}}, "deletedReminder": "Reminder deleted!", "@deletedReminder": {"type": "text", "placeholders": {}}, "fillAllFields": "Please fill in the fields!", "@fillAllFields": {"type": "text", "placeholders": {}}, "version": "Version", "@version": {"type": "text", "placeholders": {}}, "share": "Share The App", "@share": {"type": "text", "placeholders": {}}, "facebook": "Follow us on Facebook", "@facebook": {"type": "text", "placeholders": {}}, "addZekrBookmark": "The Zekr has been added to favourites", "@addZekrBookmark": {"type": "text", "placeholders": {}}, "deletedZekrBookmark": "The Zekr has been removed from favourites!", "@deletedZekrBookmark": {"type": "text", "placeholders": {}}, "pageNo": "Page No", "@pageNo": {"type": "text", "placeholders": {}}, "lastRead": "Last Read", "@lastRead": {"type": "text", "placeholders": {}}, "copyAyah": "The verse has been copied", "@copyAyah": {"type": "text", "placeholders": {}}, "copyTafseer": "The Tafseer has been copied", "@copyTafseer": {"type": "text", "placeholders": {}}, "online": "Online", "@online": {"type": "text", "placeholders": {}}, "download": "Download", "@download": {"type": "text", "placeholders": {}}, "noInternet": "The device is not connected to the Internet!", "@noInternet": {"type": "text", "placeholders": {}}, "mobileDataAyat": "Note: You are using cellular data to download verses!", "@mobileDataAyat": {"type": "text", "placeholders": {}}, "mobileDataSurahs": "Note: You are using cellular data to download the surahs!", "@mobileDataSurahs": {"type": "text", "placeholders": {}}, "mobileDataListen": "Note: You are using cellular data to listen to surahs!", "@mobileDataListen": {"type": "text", "placeholders": {}}, "choiceAyah": "Please choose the verse first!", "@choiceAyah": {"type": "text", "placeholders": {}}, "stopSigns": "Stop Signs", "@stopSigns": {"type": "text", "placeholders": {}}, "aboutApp": "About App", "@aboutApp": {"type": "text", "placeholders": {}}, "readMore": "Read More", "@readMore": {"type": "text", "placeholders": {}}, "readLess": "Read Less", "@readLess": {"type": "text", "placeholders": {}}, "ok": "Ok", "@ok": {"type": "text", "placeholders": {}}, "cancel": "Cancel", "@cancel": {"type": "text", "placeholders": {}}, "searchToSurah": "Search To Surah", "@searchToSurah": {"type": "text", "placeholders": {}}, "addReminder": "Add <PERSON> Reminder", "@addReminder": {"type": "text", "placeholders": {}}, "lastListen": "Last Listen", "@lastListen": {"type": "text", "placeholders": {}}, "hizb": "Hizb", "@hizb": {"type": "text", "placeholders": {}}, "shareText": "As text", "@shareText": {"type": "text", "placeholders": {}}, "shareImage": "As an image", "@shareImage": {"type": "text", "placeholders": {}}, "shareImageWTrans": "As an image with additional content", "@shareImageWTrans": {"type": "text", "placeholders": {}}, "shareTrans": "Note: Sharing the interpretation in an image only supports the interpretation of Al-Saadi, because the interpretation is not lengthy.", "@shareTrans": {"type": "text", "placeholders": {}}, "translation": "Translation", "@translation": {"type": "text", "placeholders": {}}, "search": "Search", "@search": {"type": "text", "placeholders": {}}, "surahsList": "Surahs List", "@surahsList": {"type": "text", "placeholders": {}}, "bookmarksList": "Bookmarks List", "@bookmarksList": {"type": "text", "placeholders": {}}, "copy": "Copy", "@copy": {"type": "text", "placeholders": {}}, "shareTafseer": "Share", "@shareTafseer": {"type": "text", "placeholders": {}}, "addToBookmark": "Add To Bookmark", "@addToBookmark": {"type": "text", "placeholders": {}}, "repeatSurah": "<PERSON><PERSON>", "@repeatSurah": {"type": "text", "placeholders": {}}, "pauseSurah": "<PERSON><PERSON>", "@pauseSurah": {"type": "text", "placeholders": {}}, "replaySurah": "<PERSON><PERSON>", "@replaySurah": {"type": "text", "placeholders": {}}, "skipToPrevious": "<PERSON>p <PERSON>", "@skipToPrevious": {"type": "text", "placeholders": {}}, "deletedPlayList": "The playlist has been deleted", "@deletedPlayList": {"type": "text", "placeholders": {}}, "createPlayList": "Create the playlist", "@createPlayList": {"type": "text", "placeholders": {}}, "playList": "Playlists", "@playList": {"type": "text", "placeholders": {}}, "playListName": "Type the name of the playlist", "@playListName": {"type": "text", "placeholders": {}}, "from": "From", "@from": {"type": "text", "placeholders": {}}, "to": "To", "@to": {"type": "text", "placeholders": {}}, "quran": "The Holy Quran", "@quran": {"type": "text", "placeholders": {}}, "quranAudio": "Listening to Quran", "@quranAudio": {"type": "text", "placeholders": {}}, "startScreen": "Start Screen", "@startScreen": {"type": "text", "placeholders": {}}, "selectScreen": "<PERSON>ose Start Screen", "@selectScreen": {"type": "text", "placeholders": {}}, "dailyZeker": "Prayer of the Day", "@dailyZeker": {"type": "text", "placeholders": {}}, "lang": "English", "@lang": {"type": "text", "placeholders": {}}, "juz": "Part", "@juz": {"type": "text", "placeholders": {}}, "sajda": "Prostration", "@sajda": {"type": "text", "placeholders": {}}, "pages": "Pages", "@pages": {"type": "text", "placeholders": {}}, "ayahs": "<PERSON><PERSON><PERSON>", "@ayahs": {"type": "text", "placeholders": {}}, "eidGreetingContent": "May Allah accept from us and from you fasting, prayer, and righteous deeds.", "@eidGreetingContent": {"type": "text", "placeholders": {}}, "eidGreetingContent2": "Every year and you are closer to <PERSON>.", "@eidGreetingContent2": {"type": "text", "placeholders": {}}, "eidGreetingTitle": "<PERSON><PERSON>", "@eidGreetingTitle": {"type": "text", "placeholders": {}}, "ramadhanMubarak": "<PERSON><PERSON><PERSON>", "@ramadhanMubarak": {"type": "text", "placeholders": {}}, "home": "Home", "@home": {"type": "text", "placeholders": {}}, "skip": "<PERSON><PERSON>", "@skip": {"type": "text", "placeholders": {}}, "What's New": "What's New", "@What's New": {"type": "text", "placeholders": {}}, "What'sNewTitle": "New and user-friendly interface:", "@What'sNewTitle": {"type": "text", "placeholders": {}}, "ourApps": "Our Apps", "@ourApps": {"type": "text", "placeholders": {}}, "choseQuran": "Choose Reading Mode", "@choseQuran": {"type": "text", "placeholders": {}}, "blueMode": "Blue", "@blueMode": {"type": "text", "placeholders": {}}, "brownMode": "<PERSON>", "@brownMode": {"type": "text", "placeholders": {}}, "oldMode": "Old", "@oldMode": {"type": "text", "placeholders": {}}, "darkMode": "Dark", "@darkMode": {"type": "text", "placeholders": {}}, "Sunday": "Sunday", "@Sunday": {"type": "text", "placeholders": {}}, "Monday": "Monday", "@Monday": {"type": "text", "placeholders": {}}, "Tuesday": "Tuesday", "@Tuesday": {"type": "text", "placeholders": {}}, "Wednesday": "Wednesday", "@Wednesday": {"type": "text", "placeholders": {}}, "Thursday": "Thursday", "@Thursday": {"type": "text", "placeholders": {}}, "Friday": "Friday", "@Friday": {"type": "text", "placeholders": {}}, "Saturday": "Saturday", "@Saturday": {"type": "text", "placeholders": {}}, "RemainsUntilTheEndOf": "Remains until the end of", "@RemainsUntilTheEndOf": {"type": "text", "placeholders": {}}, "Muharram": "<PERSON><PERSON><PERSON>", "@Muharram": {"type": "text", "placeholders": {}}, "Safar": "<PERSON><PERSON>", "@Safar": {"type": "text", "placeholders": {}}, "Rabi' Al-Awwal": "<PERSON><PERSON><PERSON>", "@Rabi' Al-Awwal": {"type": "text", "placeholders": {}}, "Rabi' Al-Thani": "<PERSON><PERSON><PERSON>", "@Rabi' Al-Thani": {"type": "text", "placeholders": {}}, "Jumada Al-Awwal": "<PERSON><PERSON>", "@Jumada Al-Awwal": {"type": "text", "placeholders": {}}, "Jumada Al-Thani": "<PERSON><PERSON>", "@Jumada Al-Thani": {"type": "text", "placeholders": {}}, "Rajab": "<PERSON><PERSON>", "@Rajab": {"type": "text", "placeholders": {}}, "Sha'aban": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@Sha'aban": {"type": "text", "placeholders": {}}, "Ramadan": "<PERSON><PERSON>", "@Ramadan": {"type": "text", "placeholders": {}}, "Shawwal": "<PERSON><PERSON>", "@Shawwal": {"type": "text", "placeholders": {}}, "Dhu Al-Qi'dah": "<PERSON><PERSON>", "@Dhu Al-Qi'dah": {"type": "text", "placeholders": {}}, "Dhu Al-Hijjah": "<PERSON>hu <PERSON>", "@Dhu Al-Hijjah": {"type": "text", "placeholders": {}}, "Day": "Day", "@Day": {"type": "text", "placeholders": {}}, "Days": "Days", "@Days": {"type": "text", "placeholders": {}}, "hijriNote": "Note: Please be aware that there might be a slight difference in the specified times as the Hijri calendar is based on the direct sighting of the crescent moon. We make every effort to ensure the accuracy of the announced dates, taking into account that the calendar will be corrected in case of the moon sighting.", "@hijriNote": {"type": "text", "placeholders": {}}, "AH": "AH", "@AH": {"type": "text", "placeholders": {}}, "Fajr": "<PERSON><PERSON><PERSON>", "@Fajr": {"type": "text", "placeholders": {}}, "Sunrise": "Sunrise", "@Sunrise": {"type": "text", "placeholders": {}}, "Dhuhr": "<PERSON><PERSON><PERSON>", "@Dhuhr": {"type": "text", "placeholders": {}}, "Asr": "<PERSON><PERSON>", "@Asr": {"type": "text", "placeholders": {}}, "Maghrib": "<PERSON><PERSON><PERSON><PERSON>", "@Maghrib": {"type": "text", "placeholders": {}}, "Isha": "<PERSON><PERSON>", "@Isha": {"type": "text", "placeholders": {}}, "Last Third": "The last third", "@Last Third": {"type": "text", "placeholders": {}}, "Middle of the Night": "Midnight", "@Middle of the Night": {"type": "text", "placeholders": {}}, "tafsirLibrary": "Tafsir Library", "@tafsirLibrary": {"type": "text", "placeholders": {}}, "allBooks": "All Books", "@allBooks": {"type": "text", "placeholders": {}}, "myLibrary": "My Library", "@myLibrary": {"type": "text", "placeholders": {}}, "bookmark": "Bookmarks", "@bookmark": {"type": "text", "placeholders": {}}, "chapterBook": "Chapters of the Book", "@chapterBook": {"type": "text", "placeholders": {}}, "aboutBook": "About the Book", "@aboutBook": {"type": "text", "placeholders": {}}, "khatmah": "<PERSON><PERSON><PERSON>", "@khatmah": {"type": "text", "placeholders": {}}, "createKhatmah": "Create the Khatmah", "@createKhatmah": {"type": "text", "placeholders": {}}, "khatmahName": "Name of the Khatmah", "@khatmahName": {"type": "text", "placeholders": {}}, "addKhatmah": "Add a <PERSON>", "@addKhatmah": {"type": "text", "placeholders": {}}, "duration": "Duration", "@duration": {"type": "text", "placeholders": {}}, "divisionBySahabah": "Division by the Sahabah", "@divisionBySahabah": {"type": "text", "placeholders": {}}, "choiceColor": "Choose the color", "@choiceColor": {"type": "text", "placeholders": {}}, "searchInBooks": "Search in Books", "@searchInBooks": {"type": "text", "placeholders": {}}, "hasPassed": "Has passed", "@hasPassed": {"type": "text", "placeholders": {}}, "downloading": "Downloading...", "@downloading": {"type": "text", "placeholders": {}}, "startHijriYear": "Start of the Hijri Year", "@startHijriYear": {"type": "text", "placeholders": {}}, "reminderToFastTasoo'a": "Reminder for the Fasting of Tasu'a", "@reminderToFastTasoo'a": {"type": "text", "placeholders": {}}, "reminderToFastAshura": "Reminder for the Fasting of Ashura", "@reminderToFastAshura": {"type": "text", "placeholders": {}}, "ramadhan": "Ramadan Month", "@ramadhan": {"type": "text", "placeholders": {}}, "nightOfQadir": "Reminder for Laylat al-Qadr", "@nightOfQadir": {"type": "text", "placeholders": {}}, "EidAl-Fitr": "<PERSON><PERSON>", "@EidAl-Fitr": {"type": "text", "placeholders": {}}, "sexShawwal": "Reminder for the Fasting of Six Days of Shawwal", "@sexShawwal": {"type": "text", "placeholders": {}}, "arafahReminder": "Reminder for the Fasting on the Day of Arafah", "@arafahReminder": {"type": "text", "placeholders": {}}, "tenDaysOfDhul-Hijjah": "The First Ten Days of Dhul Hijjah", "@tenDaysOfDhul-Hijjah": {"type": "text", "placeholders": {}}, "EidAl-Adha": "<PERSON><PERSON>", "@EidAl-Adha": {"type": "text", "placeholders": {}}, "choiceBackgroundColor": "Choose background color", "@choiceBackgroundColor": {"type": "text", "placeholders": {}}, "reset": "Reset", "@reset": {"type": "text", "placeholders": {}}, "Medinan": "Medinan", "@Medinan": {"type": "text", "placeholders": {}}, "Meccan": "Meccan", "@Meccan": {"type": "text", "placeholders": {}}, "surahNames": "Names of the Surah", "@surahNames": {"type": "text", "placeholders": {}}, "aboutSurah": "About the Surah", "@aboutSurah": {"type": "text", "placeholders": {}}, "notification": "Notifications", "@notification": {"type": "text", "placeholders": {}}, "noNotifications": "No notifications", "@noNotifications": {"type": "text", "placeholders": {}}, "reminders": "Reminders", "@reminders": {"type": "text", "placeholders": {}}, "customReminder": "Custom reminder", "@customReminder": {"type": "text", "placeholders": {}}, "addMore": "Add more", "@addMore": {"type": "text", "placeholders": {}}, "What'sNewDetails10": "۞ Added support for Kurdish, Turkish, and Russian languages in the app.\n۞ Added more readers.", "@What'sNewDetails10": {"type": "text", "placeholders": {}}, "chooseDhekr": "Set a time for reflection.", "@chooseDhekr": {"type": "text", "placeholders": {}}, "selectTime": "Choose the remembrance.", "@selectTime": {"type": "text", "placeholders": {}}, "downloadBookFirst": "Please download the book first.", "@downloadBookFirst": {"type": "text", "placeholders": {}}, "lastDayOf": "The last day of the month of", "@lastDayOf": {"type": "text", "placeholders": {}}, "booksDownloaded": "The book has been downloaded.", "@booksDownloaded": {"type": "text", "placeholders": {}}, "booksDeleted": "The book has been deleted.", "@booksDeleted": {"type": "text", "placeholders": {}}, "noBooksDownloaded": "You have not downloaded any book yet.", "@noBooksDownloaded": {"type": "text", "placeholders": {}}, "notifyQuranBody": "You stopped at page @currentPageNumber in the Quran, would you like to continue?", "@notifyQuranBody": {"type": "text", "placeholders": {"currentPageNumber": {}}}, "notifyAdhkarBody": "Don't forget to read @adhkarType", "@notifyAdhkarBody": {"type": "text", "placeholders": {"adhkarType": {}}}, "notifyBooksBody": "Don't forget to continue reading the book @bookName", "@notifyBooksBody": {"type": "text", "placeholders": {"bookName": {}}}, "notifyListenBody": "Don't forget to continue listening to the surahs", "@notifyListenBody": {"type": "text", "placeholders": {"bookName": {}}}, "lastSearch": "Last Search", "@lastSearch": {"type": "text", "placeholders": {}}, "ashura": "<PERSON><PERSON>", "@ashura": {"type": "text", "placeholders": {}}, "arafah": "Day of Arafah", "@arafah": {"type": "text", "placeholders": {}}, "editHijriDay": "Edit <PERSON><PERSON><PERSON>", "@editHijriDay": {"type": "text", "placeholders": {}}, "defaultFontText": "<PERSON><PERSON><PERSON>", "@defaultFontText": {"type": "text", "placeholders": {}}, "downloadedFontsText": "Quran Font", "@downloadedFontsText": {"type": "text", "placeholders": {}}, "fonts": "Fonts", "@fonts": {"type": "text", "placeholders": {}}, "fontsNotes": "For a better experience and optimal Quran appearance, please download its fonts.", "@fontsNotes": {"type": "text", "placeholders": {}}, "calenderSettings": "Calendar Settings", "@calenderSettings": {"type": "text", "placeholders": {}}, "islamicCalendar": "Islamic Calendar", "hijriCalendar": "Hijri Calendar", "calendar": "Calendar", "events": "Events", "year": "Year", "month": "Month", "hijri": "<PERSON><PERSON><PERSON>", "Sun": "Sun", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat"}