import 'package:quran_library/quran.dart';

List<TafsirNameModel> tafsirNamesList = [
  TafsirNameModel(
    name: 'تفسير ابن كثير',
    bookName: 'تفسير القرآن العظيم',
    databaseName: 'ibnkatheerV3.sqlite',
  ),
  TafsirNameModel(
    name: 'تفسير البغوي',
    bookName: 'معالم التنزيل في تفسير القرآن',
    databaseName: 'baghawyV3.db',
  ),
  TafsirNameModel(
    name: 'تفسير القرطبي',
    bookName: 'الجامع لأحكام القرآن',
    databaseName: 'qurtubiV3.db',
  ),
  TafsirNameModel(
    name: 'تفسير السعدي',
    bookName: 'تيسير الكريم الرحمن',
    databaseName: 'saadiV4.db',
  ),
  TafsirNameModel(
    name: 'تفسير الطبري',
    bookName: 'جامع البيان عن تأويل آي القرآن',
    databaseName: 'tabariV3.db',
  )
];
