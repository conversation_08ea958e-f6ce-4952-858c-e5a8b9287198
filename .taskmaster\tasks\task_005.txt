# Task ID: 5
# Title: Create Quran Data Models and Repository
# Status: done
# Dependencies: 3
# Priority: high
# Description: Develop data models and repository for Quranic text, including surahs, verses, translations, and tafsir content.
# Details:
1. Create data models in lib/domain/entities/:
   - Surah model (id, name, verses count, revelation type)
   - Verse model (id, text, translation, surah reference)
   - Tafsir model (id, verse reference, source, content)
   - Recitation model (id, verse reference, reciter, audio URL)
2. Implement repositories in lib/data/repositories/:
   - QuranRepository for accessing Quranic text
   - TafsirRepository for interpretations
   - RecitationRepository for audio files
3. Create data sources:
   - Local data source using sqflite for offline access
   - Remote data source using Supabase
4. Implement caching strategy for offline access
5. Create data synchronization mechanism

# Test Strategy:
Unit test all repositories with mock data sources. Verify correct data mapping between models and database. Test offline functionality by disabling network and ensuring data is still accessible. Verify data synchronization works when coming back online.
