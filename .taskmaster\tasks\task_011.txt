# Task ID: 11
# Title: Implement AI Integration for Insights Generation
# Status: done
# Dependencies: 3, 5
# Priority: high
# Description: Implement the missing OpenAI GPT integration for generating contextual insights on Quranic verses with proper caching. This feature was previously documented but not implemented in the codebase.
# Details:
1. Create AI service in lib/core/services/:
   - Create ai_service.dart to implement OpenAI API client
   - Create insights_service.dart for structured prompt templates
   - Handle API rate limiting and errors
   - Ensure compatibility with existing user_preferences table that has enable_ai_insights flag
2. Develop insight generation logic:
   - Create InsightGenerator class
   - Implement different insight types (thematic, practical, historical)
   - Structure response parsing
   - Connect to existing database schema
3. Implement caching system:
   - Cache insights in local database
   - Set up expiration policy
   - Implement background refresh
4. Create Edge Functions for secure API access:
   - Set up Supabase Edge Functions
   - Implement authentication and rate limiting
   - Create logging for prompt and response tracking
5. Implement offline access to cached insights
6. Create AI insights UI components:
   - Develop screens for displaying AI-generated insights
   - Implement UI for different insight types
   - Add toggle in settings to enable/disable AI features using existing enable_ai_insights flag
7. Connect to existing database migrations:
   - Review existing Supabase schema
   - Ensure AI insights tables align with existing database design

# Test Strategy:
Test insight generation with various verses. Verify caching works by generating insights and accessing them offline. Test error handling by simulating API failures. Measure response times and optimize where needed. Verify different insight types produce appropriate content. Ensure UI components display insights correctly. Test enable/disable functionality using the existing user preferences flag.
